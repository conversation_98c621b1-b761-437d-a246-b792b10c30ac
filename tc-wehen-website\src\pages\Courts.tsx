import React from 'react'
import { Helmet } from 'react-helmet-async'
import { Clock, MapPin, Wrench, ExternalLink, Calendar, Users } from 'lucide-react'
import Card from '../components/ui/Card'
import Button from '../components/ui/Button'
import courtsOverview from '../assets/Obenansicht-Plätze.PNG'
import courts1 from '../assets/Plätze.PNG'
import courts2 from '../assets/Plätze2.PNG'
import courts3 from '../assets/Plätze3.PNG'
import terrace from '../assets/Terrassenansicht-Plätze.PNG'

const Courts: React.FC = () => {
  const facilities = [
    {
      icon: MapPin,
      title: '6 Sandplätze',
      description: 'Gepflegte Sandplätze in ruhiger Lage mit optimaler Ausrichtung',
    },
    {
      icon: Users,
      title: 'Clubhaus',
      description: 'Gemütliches Clubhaus mit Terrasse für geselliges Beisammensein',
    },
    {
      icon: Wrench,
      title: 'Moderne Ausstattung',
      description: 'Professionelle Platzpflege und hochwertige Spielfeldausstattung',
    },
    {
      icon: Clock,
      title: 'Flexible Spielzeiten',
      description: '<PERSON>pielen Sie von früh bis spät in entspannter Atmosphäre',
    },
  ]

  const courtRules = [
    'Plätze sind nur für Vereinsmitglieder und deren Gäste zugänglich',
    'Reservierung über das Online-System erforderlich',
    'Spielzeit: Maximal 2 Stunden pro Reservierung',
    'Tennisschuhe mit heller Sohle sind Pflicht',
    'Nach dem Spiel bitte Platz abziehen',
    'Clubhaus-Öffnungszeiten beachten',
  ]

  const openingHours = [
    { day: 'Montag - Freitag', time: '08:00 - 22:00 Uhr' },
    { day: 'Samstag', time: '08:00 - 22:00 Uhr' },
    { day: 'Sonntag', time: '08:00 - 20:00 Uhr' },
  ]

  return (
    <>
      <Helmet>
        <title>Plätze & Anlage - TC Wehen e.V. | 6 Sandplätze in Taunusstein</title>
        <meta 
          name="description" 
          content="Entdecken Sie unsere 6 gepflegten Sandplätze und die moderne Tennisanlage des TC Wehen e.V. in Taunusstein. Jetzt Platz reservieren!" 
        />
      </Helmet>

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-gray-900 to-gray-800 text-white overflow-hidden">
        <div className="absolute inset-0">
          <img 
            src={terrace} 
            alt="Tennisanlage TC Wehen" 
            className="w-full h-full object-cover opacity-30"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-gray-900/80 to-gray-900/40"></div>
        </div>
        
        <div className="relative container-max section-padding">
          <div className="max-w-3xl">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Unsere Tennisanlage
            </h1>
            <p className="text-xl text-gray-200 mb-8 leading-relaxed">
              6 gepflegte Sandplätze in idyllischer Lage bieten optimale Bedingungen 
              für Ihr Tennisspiel. Moderne Ausstattung und familiäre Atmosphäre erwarten Sie.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button href="https://wehen.tennisplatz.info" size="lg">
                <ExternalLink className="h-5 w-5 mr-2" />
                Platz reservieren
              </Button>
              <Button to="/kontakt" variant="outline" size="lg">
                Kontakt aufnehmen
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Facilities Overview */}
      <section className="section-padding bg-white">
        <div className="container-max">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Unsere Ausstattung
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Moderne Tennisanlage mit allem, was Sie für ein perfektes Spielerlebnis benötigen
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {facilities.map((facility, index) => {
              const IconComponent = facility.icon
              return (
                <Card key={index} className="text-center">
                  <div className="bg-accent-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <IconComponent className="h-8 w-8 text-accent-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {facility.title}
                  </h3>
                  <p className="text-gray-600">
                    {facility.description}
                  </p>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Image Gallery */}
      <section className="section-padding bg-gray-50">
        <div className="container-max">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 text-center mb-12">
            Impressionen unserer Anlage
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <img 
                src={courtsOverview} 
                alt="Übersicht Tennisplätze" 
                className="w-full h-64 lg:h-80 object-cover rounded-xl shadow-lg"
              />
            </div>
            <div className="space-y-6">
              <img 
                src={courts1} 
                alt="Tennisplätze Ansicht 1" 
                className="w-full h-36 lg:h-36 object-cover rounded-xl shadow-lg"
              />
              <img 
                src={courts2} 
                alt="Tennisplätze Ansicht 2" 
                className="w-full h-36 lg:h-36 object-cover rounded-xl shadow-lg"
              />
            </div>
            <div className="md:col-span-2 lg:col-span-1">
              <img 
                src={courts3} 
                alt="Tennisplätze Ansicht 3" 
                className="w-full h-64 object-cover rounded-xl shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Opening Hours & Rules */}
      <section className="section-padding bg-white">
        <div className="container-max">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Opening Hours */}
            <Card>
              <div className="flex items-center mb-6">
                <Clock className="h-6 w-6 text-accent-600 mr-3" />
                <h3 className="text-2xl font-bold text-gray-900">Öffnungszeiten</h3>
              </div>
              <div className="space-y-4">
                {openingHours.map((hours, index) => (
                  <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                    <span className="font-medium text-gray-900">{hours.day}</span>
                    <span className="text-gray-600">{hours.time}</span>
                  </div>
                ))}
              </div>
              <div className="mt-6 p-4 bg-accent-50 rounded-lg">
                <p className="text-sm text-accent-800">
                  <strong>Hinweis:</strong> Platzreservierung über unser Online-System erforderlich
                </p>
              </div>
            </Card>

            {/* Court Rules */}
            <Card>
              <div className="flex items-center mb-6">
                <Calendar className="h-6 w-6 text-accent-600 mr-3" />
                <h3 className="text-2xl font-bold text-gray-900">Platzregeln</h3>
              </div>
              <ul className="space-y-3">
                {courtRules.map((rule, index) => (
                  <li key={index} className="flex items-start">
                    <span className="bg-accent-100 text-accent-600 rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium mr-3 mt-0.5 flex-shrink-0">
                      {index + 1}
                    </span>
                    <span className="text-gray-600">{rule}</span>
                  </li>
                ))}
              </ul>
            </Card>
          </div>
        </div>
      </section>

      {/* Reservation CTA */}
      <section className="section-padding bg-accent-600 text-white">
        <div className="container-max text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Bereit für Ihr nächstes Match?
          </h2>
          <p className="text-xl text-accent-100 mb-8 max-w-2xl mx-auto">
            Reservieren Sie jetzt Ihren Platz über unser Online-Reservierungssystem 
            und genießen Sie Tennis in bester Qualität.
          </p>
          <Button 
            href="https://wehen.tennisplatz.info" 
            variant="secondary" 
            size="lg"
          >
            <ExternalLink className="h-5 w-5 mr-2" />
            Jetzt Platz reservieren
          </Button>
        </div>
      </section>
    </>
  )
}

export default Courts
