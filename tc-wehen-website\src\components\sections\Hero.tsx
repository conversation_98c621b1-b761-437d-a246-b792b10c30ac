import React from 'react'
import Button from '../ui/Button'
import courtsImage from '../../assets/Terrassenansicht-Plätze.PNG'

const Hero: React.FC = () => {
  return (
    <section className="relative bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0">
        <img 
          src={courtsImage} 
          alt="TC Wehen Tennisplätze" 
          className="w-full h-full object-cover opacity-30"
        />
        <div className="absolute inset-0 bg-gradient-to-r from-gray-900/80 to-gray-900/40"></div>
      </div>
      
      {/* Content */}
      <div className="relative container-max section-padding">
        <div className="max-w-4xl">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight mb-6">
            Will<PERSON>mmen beim
            <span className="block text-accent-400">TC Wehen e.V.</span>
          </h1>
          
          <p className="text-xl md:text-2xl text-gray-200 mb-8 leading-relaxed">
            Für <PERSON>sspieler, Hobby- und Freizeitspieler, Kinder, Jugendliche und Erwachsene: 
            Der TC Wehen bietet seinen Mitgliedern Tennis in (fast) jeder Spielstärke
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 mb-12">
            <Button to="/platzreservierung" size="lg">
              Platz reservieren
            </Button>
            <Button to="/mitgliedschaft" variant="outline" size="lg">
              Mitglied werden
            </Button>
            <Button to="/training-kurse" variant="secondary" size="lg">
              Training buchen
            </Button>
          </div>
          
          {/* Key Features */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h3 className="text-lg font-semibold mb-2">6 Sandplätze</h3>
              <p className="text-gray-300">Gepflegte Sandplätze für optimales Spielerlebnis</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h3 className="text-lg font-semibold mb-2">Familiäre Atmosphäre</h3>
              <p className="text-gray-300">Freundliche Gemeinschaft für alle Altersgruppen</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h3 className="text-lg font-semibold mb-2">Alle Spielstärken</h3>
              <p className="text-gray-300">Von Anfänger bis Mannschaftsspieler</p>
            </div>
          </div>
        </div>
      </div>
      
      {/* Decorative Elements */}
      <div className="absolute top-20 right-20 w-32 h-32 bg-accent-500/20 rounded-full blur-3xl"></div>
      <div className="absolute bottom-20 left-20 w-48 h-48 bg-primary-500/20 rounded-full blur-3xl"></div>
    </section>
  )
}

export default Hero
