import React from 'react'
import { Helm<PERSON> } from 'react-helmet-async'
import { Check, Download, Users, Heart } from 'lucide-react'
import Card from '../components/ui/Card'
import Button from '../components/ui/Button'

const Membership: React.FC = () => {
  const membershipTypes = [
    {
      name: 'Erwachsene',
      price: '180€',
      period: 'pro Jahr',
      features: [
        'Unbegrenzte Platznutzung',
        'Teilnahme an Vereinsveranstaltungen',
        'Mannschaftstennis möglich',
        'Clubhaus-Nutzung',
        'Vergünstigungen bei Kursen',
      ],
    },
    {
      name: 'Jugendl<PERSON> (bis 18)',
      price: '80€',
      period: 'pro Jahr',
      features: [
        'Unbegrenzte Platznutzung',
        'Jugendtraining inklusive',
        'Teilnahme an Jugendturnieren',
        'Clubhaus-Nutzung',
        'Betreuung durch Jugendwart',
      ],
    },
    {
      name: 'Familie',
      price: '320€',
      period: 'pro Jahr',
      features: [
        '2 Erwachsene + Kinder bis 18',
        'Alle Vorteile der Einzelmitgliedschaft',
        'Familienrabatt',
        'Gemeinsame Aktivitäten',
        'Besondere Familienevents',
      ],
    },
  ]

  const benefits = [
    'Gepflegte Sandplätze in ruhiger Lage',
    'Familiäre Vereinsatmosphäre',
    'Regelmäßige Vereinsveranstaltungen',
    'Professionelle Trainingsmöglichkeiten',
    'Mannschaftstennis in verschiedenen Ligen',
    'Gemütliches Clubhaus mit Terrasse',
  ]

  return (
    <>
      <Helmet>
        <title>Mitgliedschaft - TC Wehen e.V. | Mitglied werden Taunusstein</title>
        <meta 
          name="description" 
          content="Werden Sie Mitglied im TC Wehen e.V. Faire Beiträge, gepflegte Plätze und familiäre Atmosphäre. Jetzt Mitgliedsantrag herunterladen!" 
        />
      </Helmet>

      <section className="bg-gradient-to-br from-gray-900 to-gray-800 text-white section-padding">
        <div className="container-max">
          <div className="max-w-3xl">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Mitgliedschaft
            </h1>
            <p className="text-xl text-gray-200 leading-relaxed">
              Werden Sie Teil unserer Tennisfamilie! Genießen Sie alle Vorteile einer 
              Mitgliedschaft im TC Wehen e.V. und erleben Sie Tennis in familiärer Atmosphäre.
            </p>
          </div>
        </div>
      </section>

      <section className="section-padding bg-white">
        <div className="container-max">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Mitgliedsbeiträge 2024
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Faire und transparente Beiträge für alle Altersgruppen
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            {membershipTypes.map((type, index) => (
              <Card key={index} hover className="text-center relative">
                {index === 1 && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-accent-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                      Beliebt
                    </span>
                  </div>
                )}
                
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  {type.name}
                </h3>
                
                <div className="mb-6">
                  <span className="text-4xl font-bold text-accent-600">{type.price}</span>
                  <span className="text-gray-600 ml-2">{type.period}</span>
                </div>
                
                <ul className="space-y-3 mb-8">
                  {type.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center text-left">
                      <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                      <span className="text-gray-600">{feature}</span>
                    </li>
                  ))}
                </ul>
                
                <Button className="w-full">
                  Jetzt beitreten
                </Button>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <section className="section-padding bg-gray-50">
        <div className="container-max">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Ihre Vorteile als Mitglied
              </h2>
              <ul className="space-y-4">
                {benefits.map((benefit, index) => (
                  <li key={index} className="flex items-start">
                    <Heart className="h-6 w-6 text-accent-600 mr-3 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{benefit}</span>
                  </li>
                ))}
              </ul>
            </div>
            
            <Card>
              <div className="text-center">
                <Users className="h-16 w-16 text-accent-600 mx-auto mb-6" />
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  Mitgliedsantrag
                </h3>
                <p className="text-gray-600 mb-6">
                  Laden Sie unseren Mitgliedsantrag herunter, füllen Sie ihn aus 
                  und senden Sie ihn an uns zurück.
                </p>
                <div className="space-y-4">
                  <Button className="w-full">
                    <Download className="h-5 w-5 mr-2" />
                    Antrag herunterladen (PDF)
                  </Button>
                  <Button to="/kontakt" variant="outline" className="w-full">
                    Fragen zur Mitgliedschaft
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </section>
    </>
  )
}

export default Membership
