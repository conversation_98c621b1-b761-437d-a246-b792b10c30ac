import React from 'react'
import { Helmet } from 'react-helmet-async'
import { Users, Trophy, Calendar, ExternalLink } from 'lucide-react'
import Card from '../components/ui/Card'
import But<PERSON> from '../components/ui/Button'

const Teams: React.FC = () => {
  const teams = [
    {
      name: 'Herren 1',
      league: 'Bezirksliga',
      captain: '<PERSON>',
      players: 8,
      season: '2024',
    },
    {
      name: 'Herren 2',
      league: 'Kreisliga A',
      captain: '<PERSON>',
      players: 6,
      season: '2024',
    },
    {
      name: 'Damen 1',
      league: 'Bezirksklasse',
      captain: '<PERSON>',
      players: 6,
      season: '2024',
    },
    {
      name: 'Jugend U18',
      league: 'Bezirksjugendliga',
      captain: '<PERSON>',
      players: 4,
      season: '2024',
    },
  ]

  return (
    <>
      <Helmet>
        <title>Mannschaften - TC Wehen e.V. | Tennis Teams Taunusstein</title>
        <meta 
          name="description" 
          content="Unsere Tennismannschaften des TC Wehen e.V. spielen in verschiedenen Ligen. Erfahren Sie mehr über unsere Teams und Spielpläne." 
        />
      </Helmet>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-gray-900 to-gray-800 text-white section-padding">
        <div className="container-max">
          <div className="max-w-3xl">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Unsere Mannschaften
            </h1>
            <p className="text-xl text-gray-200 leading-relaxed">
              Der TC Wehen e.V. ist stolz auf seine aktiven Mannschaften, die in verschiedenen 
              Ligen um Punkte und Siege kämpfen. Vom Nachwuchs bis zu den Senioren - 
              bei uns findet jeder sein Team.
            </p>
          </div>
        </div>
      </section>

      {/* Teams Overview */}
      <section className="section-padding bg-white">
        <div className="container-max">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Saison 2024
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Unsere Mannschaften kämpfen in verschiedenen Ligen um Erfolg und Spaß am Tennissport
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {teams.map((team, index) => (
              <Card key={index} hover className="text-center">
                <div className="bg-accent-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="h-8 w-8 text-accent-600" />
                </div>
                
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {team.name}
                </h3>
                
                <p className="text-accent-600 font-medium mb-4">
                  {team.league}
                </p>
                
                <div className="space-y-2 text-sm text-gray-600">
                  <p><strong>Mannschaftsführer:</strong> {team.captain}</p>
                  <p><strong>Spieler:</strong> {team.players}</p>
                  <p><strong>Saison:</strong> {team.season}</p>
                </div>
              </Card>
            ))}
          </div>
          
          <div className="text-center">
            <Button href="#" variant="outline" size="lg">
              <ExternalLink className="h-5 w-5 mr-2" />
              Zu nuLiga (Spielpläne & Ergebnisse)
            </Button>
          </div>
        </div>
      </section>

      {/* Join Team CTA */}
      <section className="section-padding bg-gray-50">
        <div className="container-max">
          <div className="text-center max-w-3xl mx-auto">
            <Trophy className="h-16 w-16 text-accent-600 mx-auto mb-6" />
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Lust auf Mannschaftstennis?
            </h2>
            <p className="text-lg text-gray-600 mb-8">
              Sie spielen gerne Tennis und möchten Teil einer Mannschaft werden? 
              Wir freuen uns über neue Spielerinnen und Spieler in allen Altersklassen!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button to="/kontakt">
                Kontakt aufnehmen
              </Button>
              <Button to="/mitgliedschaft" variant="outline">
                Mitglied werden
              </Button>
            </div>
          </div>
        </div>
      </section>
    </>
  )
}

export default Teams
