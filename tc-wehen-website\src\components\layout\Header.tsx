import React, { useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { Menu, X, Phone, Mail } from 'lucide-react'
import logoTCW from '../../assets/Logo-TCW.PNG'

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const location = useLocation()

  const navigation = [
    { name: 'Startseite', href: '/' },
    { name: 'Über uns', href: '/ueber-uns' },
    { name: '<PERSON>lätze & Anlage', href: '/plaetze-anlage' },
    { name: 'Mannschaften', href: '/mannschaften' },
    { name: 'Training & Kurse', href: '/training-kurse' },
    { name: 'Mitgliedschaft', href: '/mitgliedschaft' },
    { name: 'Aktuelles', href: '/aktuelles' },
    { name: '<PERSON><PERSON><PERSON>', href: '/kontakt' },
  ]

  const isActive = (path: string) => location.pathname === path

  return (
    <header className="bg-white shadow-lg sticky top-0 z-50">
      {/* Top contact bar */}
      <div className="bg-gray-900 text-white py-2">
        <div className="container-max">
          <div className="flex justify-between items-center text-sm">
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <Phone className="h-4 w-4" />
                <span>06128-6598</span>
              </div>
              <div className="flex items-center space-x-2">
                <Mail className="h-4 w-4" />
                <span><EMAIL></span>
              </div>
            </div>
            <div className="hidden md:block">
              <span>Platter Str., 65232 Taunusstein</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main navigation */}
      <nav className="container-max">
        <div className="flex justify-between items-center py-4">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-3">
            <img src={logoTCW} alt="TC Wehen Logo" className="h-12 w-auto" />
            <div className="hidden sm:block">
              <h1 className="text-xl font-bold text-gray-900">TC Wehen e.V.</h1>
              <p className="text-sm text-gray-600">Tennis Club Taunusstein</p>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8">
            {navigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`text-sm font-medium transition-colors duration-200 ${
                  isActive(item.href)
                    ? 'text-accent-600 border-b-2 border-accent-600 pb-1'
                    : 'text-gray-700 hover:text-accent-600'
                }`}
              >
                {item.name}
              </Link>
            ))}
            <Link
              to="/platzreservierung"
              className="btn-primary"
            >
              Platzreservierung
            </Link>
          </div>

          {/* Mobile menu button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="lg:hidden p-2 rounded-md text-gray-700 hover:text-accent-600 hover:bg-gray-100"
          >
            {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="lg:hidden border-t border-gray-200">
            <div className="py-4 space-y-2">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  onClick={() => setIsMenuOpen(false)}
                  className={`block px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200 ${
                    isActive(item.href)
                      ? 'text-accent-600 bg-accent-50'
                      : 'text-gray-700 hover:text-accent-600 hover:bg-gray-100'
                  }`}
                >
                  {item.name}
                </Link>
              ))}
              <Link
                to="/platzreservierung"
                onClick={() => setIsMenuOpen(false)}
                className="block mx-4 mt-4 btn-primary text-center"
              >
                Platzreservierung
              </Link>
            </div>
          </div>
        )}
      </nav>
    </header>
  )
}

export default Header
