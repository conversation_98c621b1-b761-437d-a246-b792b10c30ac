import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { Send, Check, AlertCircle } from 'lucide-react'
import Button from './Button'

interface FormData {
  name: string
  email: string
  phone?: string
  subject: string
  message: string
  privacy: boolean
}

const ContactForm: React.FC = () => {
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<FormData>()

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true)
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    console.log('Form submitted:', data)
    setIsSubmitted(true)
    setIsSubmitting(false)
    reset()
    
    // Reset success message after 5 seconds
    setTimeout(() => setIsSubmitted(false), 5000)
  }

  if (isSubmitted) {
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
        <Check className="h-12 w-12 text-green-600 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-green-800 mb-2">
          Nachricht gesendet!
        </h3>
        <p className="text-green-700">
          Vielen Dank für Ihre Nachricht. Wir melden uns schnellstmöglich bei Ihnen zurück.
        </p>
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Name */}
      <div>
        <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
          Name *
        </label>
        <input
          type="text"
          id="name"
          {...register('name', { required: 'Name ist erforderlich' })}
          className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-colors ${
            errors.name ? 'border-red-300' : 'border-gray-300'
          }`}
          placeholder="Ihr vollständiger Name"
        />
        {errors.name && (
          <p className="mt-1 text-sm text-red-600 flex items-center">
            <AlertCircle className="h-4 w-4 mr-1" />
            {errors.name.message}
          </p>
        )}
      </div>

      {/* Email */}
      <div>
        <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
          E-Mail *
        </label>
        <input
          type="email"
          id="email"
          {...register('email', {
            required: 'E-Mail ist erforderlich',
            pattern: {
              value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
              message: 'Ungültige E-Mail-Adresse'
            }
          })}
          className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-colors ${
            errors.email ? 'border-red-300' : 'border-gray-300'
          }`}
          placeholder="<EMAIL>"
        />
        {errors.email && (
          <p className="mt-1 text-sm text-red-600 flex items-center">
            <AlertCircle className="h-4 w-4 mr-1" />
            {errors.email.message}
          </p>
        )}
      </div>

      {/* Phone */}
      <div>
        <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
          Telefon (optional)
        </label>
        <input
          type="tel"
          id="phone"
          {...register('phone')}
          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-colors"
          placeholder="Ihre Telefonnummer"
        />
      </div>

      {/* Subject */}
      <div>
        <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
          Betreff *
        </label>
        <select
          id="subject"
          {...register('subject', { required: 'Betreff ist erforderlich' })}
          className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-colors ${
            errors.subject ? 'border-red-300' : 'border-gray-300'
          }`}
        >
          <option value="">Bitte wählen...</option>
          <option value="mitgliedschaft">Mitgliedschaft</option>
          <option value="training">Training & Kurse</option>
          <option value="platzreservierung">Platzreservierung</option>
          <option value="mannschaften">Mannschaften</option>
          <option value="allgemein">Allgemeine Anfrage</option>
          <option value="sonstiges">Sonstiges</option>
        </select>
        {errors.subject && (
          <p className="mt-1 text-sm text-red-600 flex items-center">
            <AlertCircle className="h-4 w-4 mr-1" />
            {errors.subject.message}
          </p>
        )}
      </div>

      {/* Message */}
      <div>
        <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
          Nachricht *
        </label>
        <textarea
          id="message"
          rows={5}
          {...register('message', { required: 'Nachricht ist erforderlich' })}
          className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-colors resize-vertical ${
            errors.message ? 'border-red-300' : 'border-gray-300'
          }`}
          placeholder="Ihre Nachricht an uns..."
        />
        {errors.message && (
          <p className="mt-1 text-sm text-red-600 flex items-center">
            <AlertCircle className="h-4 w-4 mr-1" />
            {errors.message.message}
          </p>
        )}
      </div>

      {/* Privacy Checkbox */}
      <div>
        <label className="flex items-start space-x-3">
          <input
            type="checkbox"
            {...register('privacy', { required: 'Datenschutz muss akzeptiert werden' })}
            className="mt-1 h-4 w-4 text-accent-600 focus:ring-accent-500 border-gray-300 rounded"
          />
          <span className="text-sm text-gray-600">
            Ich habe die{' '}
            <a href="/datenschutz" className="text-accent-600 hover:text-accent-700 underline">
              Datenschutzerklärung
            </a>{' '}
            gelesen und stimme der Verarbeitung meiner Daten zu. *
          </span>
        </label>
        {errors.privacy && (
          <p className="mt-1 text-sm text-red-600 flex items-center">
            <AlertCircle className="h-4 w-4 mr-1" />
            {errors.privacy.message}
          </p>
        )}
      </div>

      {/* Submit Button */}
      <Button
        type="submit"
        disabled={isSubmitting}
        className="w-full"
        size="lg"
      >
        {isSubmitting ? (
          <>
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
            Wird gesendet...
          </>
        ) : (
          <>
            <Send className="h-5 w-5 mr-2" />
            Nachricht senden
          </>
        )}
      </Button>
    </form>
  )
}

export default ContactForm
