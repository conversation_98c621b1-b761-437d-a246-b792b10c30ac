{"hash": "8ac6c953", "configHash": "ef4c9316", "lockfileHash": "c48b5a43", "browserHash": "cdb31953", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "20c7a2a0", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "86c35d31", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "6f2498bf", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "a2ca77ef", "needsInterop": true}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "013ab53a", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "50f2488a", "needsInterop": true}, "react-helmet-async": {"src": "../../react-helmet-async/lib/index.esm.js", "file": "react-helmet-async.js", "fileHash": "a1462770", "needsInterop": false}, "react-hook-form": {"src": "../../react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "bfddf616", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "fa78c240", "needsInterop": false}}, "chunks": {"chunk-NYHBQTAG": {"file": "chunk-NYHBQTAG.js"}, "chunk-P5XWQMHZ": {"file": "chunk-P5XWQMHZ.js"}}}