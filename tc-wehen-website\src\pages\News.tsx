import React from 'react'
import { Helm<PERSON> } from 'react-helmet-async'
import { Calendar, ArrowRight, Tag } from 'lucide-react'
import Card from '../components/ui/Card'

const News: React.FC = () => {
  const newsArticles = [
    {
      id: 1,
      title: 'Saisonstart 2024 - Alle Mannschaften bereit für neue Herausforderungen',
      excerpt: 'Mit großer Vorfreude und optimaler Vorbereitung starten wir in die neue Tennissaison 2024. Alle Mannschaften haben fleißig trainiert und freuen sich auf spannende Matches.',
      content: 'Die Winterpause ist vorbei und unsere Tennismannschaften sind bereit für die neue Saison...',
      date: '2024-04-15',
      category: 'Mannschaften',
      author: 'Sportwart',
    },
    {
      id: 2,
      title: 'Neue Trainingszeiten für Kinder und Jugendliche ab Mai',
      excerpt: 'Ab dem 1. Mai bieten wir erweiterte Trainingszeiten für unsere jüngsten Mitglieder an. Professionelle Trainer sorgen für optimale Förderung und viel Spaß beim Training.',
      content: 'Aufgrund der großen Nachfrage erweitern wir unser Trainingsangebot...',
      date: '2024-04-10',
      category: 'Training',
      author: 'Jugendwart',
    },
    {
      id: 3,
      title: 'Clubhaus-Renovierung erfolgreich abgeschlossen',
      excerpt: 'Unser Clubhaus erstrahlt in neuem Glanz. Die umfangreichen Renovierungsarbeiten sind abgeschlossen und laden zum gemütlichen Beisammensein nach dem Spiel ein.',
      content: 'Nach monatelangen Renovierungsarbeiten können wir stolz verkünden...',
      date: '2024-04-05',
      category: 'Vereinsleben',
      author: 'Vorstand',
    },
    {
      id: 4,
      title: 'Erfolgreiche Teilnahme am Regionalen Jugendturnier',
      excerpt: 'Unsere Jugendmannschaft hat beim regionalen Turnier in Wiesbaden einen hervorragenden 3. Platz erreicht. Wir sind stolz auf die Leistung unserer jungen Talente.',
      content: 'Am vergangenen Wochenende fand das regionale Jugendturnier statt...',
      date: '2024-03-28',
      category: 'Turniere',
      author: 'Jugendwart',
    },
    {
      id: 5,
      title: 'Platzpflege und Saisonvorbereitung in vollem Gange',
      excerpt: 'Unsere Sandplätze werden intensiv für die neue Saison vorbereitet. Dank der fleißigen Helfer werden alle 6 Plätze in bestem Zustand sein.',
      content: 'Die Vorbereitungen für die neue Tennissaison laufen auf Hochtouren...',
      date: '2024-03-20',
      category: 'Anlage',
      author: 'Platzwart',
    },
    {
      id: 6,
      title: 'Mitgliederversammlung 2024 - Rückblick und Ausblick',
      excerpt: 'Bei unserer Jahreshauptversammlung blickten wir auf ein erfolgreiches Jahr 2023 zurück und stellten die Pläne für 2024 vor. Vielen Dank an alle Teilnehmer.',
      content: 'Die diesjährige Mitgliederversammlung war gut besucht...',
      date: '2024-03-15',
      category: 'Vereinsleben',
      author: 'Vorstand',
    },
  ]

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('de-DE', {
      day: '2-digit',
      month: 'long',
      year: 'numeric'
    })
  }

  const getCategoryColor = (category: string) => {
    const colors = {
      'Mannschaften': 'bg-blue-100 text-blue-800',
      'Training': 'bg-green-100 text-green-800',
      'Vereinsleben': 'bg-purple-100 text-purple-800',
      'Turniere': 'bg-yellow-100 text-yellow-800',
      'Anlage': 'bg-gray-100 text-gray-800',
    }
    return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800'
  }

  return (
    <>
      <Helmet>
        <title>Aktuelles - TC Wehen e.V. | News & Vereinsnachrichten</title>
        <meta 
          name="description" 
          content="Aktuelle Nachrichten und Neuigkeiten vom TC Wehen e.V. Erfahren Sie alles über Mannschaften, Training, Turniere und Vereinsleben." 
        />
      </Helmet>

      <section className="bg-gradient-to-br from-gray-900 to-gray-800 text-white section-padding">
        <div className="container-max">
          <div className="max-w-3xl">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Aktuelles
            </h1>
            <p className="text-xl text-gray-200 leading-relaxed">
              Bleiben Sie auf dem Laufenden über alle Neuigkeiten, Events und 
              wichtige Informationen aus unserem Tennisverein.
            </p>
          </div>
        </div>
      </section>

      <section className="section-padding bg-white">
        <div className="container-max">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-8">
              {newsArticles.map((article) => (
                <Card key={article.id} hover>
                  <div className="flex items-center justify-between mb-4">
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${getCategoryColor(article.category)}`}>
                      {article.category}
                    </span>
                    <div className="flex items-center text-gray-500 text-sm">
                      <Calendar className="h-4 w-4 mr-2" />
                      {formatDate(article.date)}
                    </div>
                  </div>
                  
                  <h2 className="text-2xl font-bold text-gray-900 mb-3 hover:text-accent-600 transition-colors">
                    {article.title}
                  </h2>
                  
                  <p className="text-gray-600 mb-4 leading-relaxed">
                    {article.excerpt}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">
                      Von {article.author}
                    </span>
                    <button className="flex items-center text-accent-600 hover:text-accent-700 font-medium transition-colors">
                      Weiterlesen
                      <ArrowRight className="h-4 w-4 ml-1" />
                    </button>
                  </div>
                </Card>
              ))}
            </div>

            {/* Sidebar */}
            <div className="space-y-8">
              {/* Categories */}
              <Card>
                <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                  <Tag className="h-5 w-5 mr-2" />
                  Kategorien
                </h3>
                <ul className="space-y-2">
                  {['Mannschaften', 'Training', 'Vereinsleben', 'Turniere', 'Anlage'].map((category) => (
                    <li key={category}>
                      <button className="text-gray-600 hover:text-accent-600 transition-colors">
                        {category}
                      </button>
                    </li>
                  ))}
                </ul>
              </Card>

              {/* Recent News */}
              <Card>
                <h3 className="text-xl font-bold text-gray-900 mb-4">
                  Neueste Beiträge
                </h3>
                <ul className="space-y-4">
                  {newsArticles.slice(0, 3).map((article) => (
                    <li key={article.id} className="border-b border-gray-100 pb-3 last:border-b-0">
                      <h4 className="font-medium text-gray-900 mb-1 line-clamp-2">
                        {article.title}
                      </h4>
                      <p className="text-sm text-gray-500">
                        {formatDate(article.date)}
                      </p>
                    </li>
                  ))}
                </ul>
              </Card>
            </div>
          </div>
        </div>
      </section>
    </>
  )
}

export default News
