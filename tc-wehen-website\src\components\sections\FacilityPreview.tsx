import React from 'react'
import { MapPin, Clock, Users, Award } from 'lucide-react'
import But<PERSON> from '../ui/Button'
import Card from '../ui/Card'
import courtsOverview from '../../assets/Obenansicht-Plätze.PNG'
import courts1 from '../../assets/Plätze.PNG'
import courts2 from '../../assets/Plätze2.PNG'

const FacilityPreview: React.FC = () => {
  const features = [
    {
      icon: MapPin,
      title: '6 Sandplätze',
      description: 'Gepflegte Sandplätze in ruhiger Lage',
    },
    {
      icon: Clock,
      title: 'Flexible Zeiten',
      description: 'Spielen Sie wann es Ihnen passt',
    },
    {
      icon: Users,
      title: 'Clubhaus',
      description: 'Gemütliches Beisammensein nach dem Spiel',
    },
    {
      icon: Award,
      title: 'Seit Jahren etabliert',
      description: 'Tradition und Erfahrung im Tennissport',
    },
  ]

  return (
    <section className="section-padding bg-white">
      <div className="container-max">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Unsere Tennisanlage
            </h2>
            <p className="text-lg text-gray-600 mb-8">
              Entdecken Sie unsere moderne Tennisanlage mit 6 gepflegten Sandplätzen in 
              idyllischer Lage in Taunusstein. Perfekt für Spieler aller Altersgruppen und Spielstärken.
            </p>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-8">
              {features.map((feature, index) => {
                const IconComponent = feature.icon
                return (
                  <div key={index} className="flex items-start space-x-3">
                    <div className="bg-accent-100 p-2 rounded-lg flex-shrink-0">
                      <IconComponent className="h-5 w-5 text-accent-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-1">
                        {feature.title}
                      </h3>
                      <p className="text-gray-600 text-sm">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                )
              })}
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4">
              <Button to="/plaetze-anlage">
                Anlage entdecken
              </Button>
              <Button to="/platzreservierung" variant="outline">
                Platz reservieren
              </Button>
            </div>
          </div>
          
          {/* Images */}
          <div className="space-y-4">
            <div className="relative">
              <img 
                src={courtsOverview} 
                alt="Übersicht der Tennisplätze" 
                className="w-full h-64 object-cover rounded-xl shadow-lg"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-xl"></div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <img 
                src={courts1} 
                alt="Tennisplätze Ansicht 1" 
                className="w-full h-32 object-cover rounded-lg shadow-md"
              />
              <img 
                src={courts2} 
                alt="Tennisplätze Ansicht 2" 
                className="w-full h-32 object-cover rounded-lg shadow-md"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default FacilityPreview
