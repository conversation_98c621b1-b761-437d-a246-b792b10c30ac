import React from 'react'
import { Helmet } from 'react-helmet-async'
import { ExternalLink, Calendar, Clock, Info } from 'lucide-react'
import Card from '../components/ui/Card'
import Button from '../components/ui/Button'

const CourtReservation: React.FC = () => {
  const reservationSteps = [
    {
      step: 1,
      title: 'Online-System öffnen',
      description: 'Klicken Sie auf den Link zum Reservierungssystem',
    },
    {
      step: 2,
      title: 'Anmelden',
      description: 'Loggen Sie sich mit Ihren Mitgliedsdaten ein',
    },
    {
      step: 3,
      title: 'Platz wählen',
      description: 'Wählen Sie den gewünschten Platz und die Zeit',
    },
    {
      step: 4,
      title: 'Bestätigen',
      description: 'Bestätigen Sie Ihre Reservierung',
    },
  ]

  const reservationRules = [
    'Reservierungen sind nur für Vereinsmitglieder möglich',
    'Maximale Spielzeit: 2 Stunden pro Reservierung',
    'Reservierung bis zu 7 Tage im Voraus möglich',
    'Stornierung bis 2 Stunden vor Spielbeginn kostenfrei',
    'Bei Nichterscheinen wird eine Gebühr erhoben',
    'Gäste müssen angemeldet und begleitet werden',
  ]

  return (
    <>
      <Helmet>
        <title>Platzreservierung - TC Wehen e.V. | Online Tennisplatz buchen</title>
        <meta 
          name="description" 
          content="Reservieren Sie Ihren Tennisplatz beim TC Wehen e.V. online. Einfach, schnell und bequem über unser Reservierungssystem." 
        />
      </Helmet>

      <section className="bg-gradient-to-br from-accent-600 to-accent-700 text-white section-padding">
        <div className="container-max">
          <div className="max-w-3xl">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Platzreservierung
            </h1>
            <p className="text-xl text-accent-100 leading-relaxed mb-8">
              Reservieren Sie Ihren Tennisplatz schnell und einfach über unser 
              Online-Reservierungssystem. Verfügbare Zeiten in Echtzeit einsehen 
              und sofort buchen.
            </p>
            <Button 
              href="https://wehen.tennisplatz.info" 
              variant="secondary" 
              size="lg"
            >
              <ExternalLink className="h-5 w-5 mr-2" />
              Zum Reservierungssystem
            </Button>
          </div>
        </div>
      </section>

      <section className="section-padding bg-white">
        <div className="container-max">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              So funktioniert's
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              In nur wenigen Schritten zu Ihrer Platzreservierung
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {reservationSteps.map((step) => (
              <Card key={step.step} className="text-center">
                <div className="bg-accent-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-accent-600 font-bold text-lg">{step.step}</span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {step.title}
                </h3>
                <p className="text-gray-600 text-sm">
                  {step.description}
                </p>
              </Card>
            ))}
          </div>
          
          <div className="text-center">
            <Button 
              href="https://wehen.tennisplatz.info" 
              size="lg"
            >
              <ExternalLink className="h-5 w-5 mr-2" />
              Jetzt reservieren
            </Button>
          </div>
        </div>
      </section>

      <section className="section-padding bg-gray-50">
        <div className="container-max">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Reservation Rules */}
            <Card>
              <div className="flex items-center mb-6">
                <Info className="h-6 w-6 text-accent-600 mr-3" />
                <h3 className="text-2xl font-bold text-gray-900">Reservierungsregeln</h3>
              </div>
              <ul className="space-y-3">
                {reservationRules.map((rule, index) => (
                  <li key={index} className="flex items-start">
                    <span className="bg-accent-100 text-accent-600 rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium mr-3 mt-0.5 flex-shrink-0">
                      {index + 1}
                    </span>
                    <span className="text-gray-600">{rule}</span>
                  </li>
                ))}
              </ul>
            </Card>

            {/* Quick Info */}
            <div className="space-y-6">
              <Card>
                <div className="flex items-center mb-4">
                  <Clock className="h-6 w-6 text-accent-600 mr-3" />
                  <h3 className="text-xl font-bold text-gray-900">Öffnungszeiten</h3>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="font-medium">Montag - Freitag</span>
                    <span className="text-gray-600">08:00 - 22:00</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">Samstag</span>
                    <span className="text-gray-600">08:00 - 22:00</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">Sonntag</span>
                    <span className="text-gray-600">08:00 - 20:00</span>
                  </div>
                </div>
              </Card>

              <Card>
                <div className="flex items-center mb-4">
                  <Calendar className="h-6 w-6 text-accent-600 mr-3" />
                  <h3 className="text-xl font-bold text-gray-900">Wichtige Hinweise</h3>
                </div>
                <ul className="space-y-2 text-gray-600">
                  <li>• 6 Sandplätze verfügbar</li>
                  <li>• Tennisschuhe mit heller Sohle erforderlich</li>
                  <li>• Platz nach dem Spiel abziehen</li>
                  <li>• Bei Problemen: 06128-6598</li>
                </ul>
              </Card>
            </div>
          </div>
        </div>
      </section>

      <section className="section-padding bg-accent-600 text-white">
        <div className="container-max text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Noch kein Mitglied?
          </h2>
          <p className="text-xl text-accent-100 mb-8 max-w-2xl mx-auto">
            Die Platzreservierung ist exklusiv für Vereinsmitglieder. 
            Werden Sie Teil unserer Tennisfamilie!
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button to="/mitgliedschaft" variant="secondary" size="lg">
              Mitglied werden
            </Button>
            <Button to="/kontakt" variant="outline" size="lg">
              Fragen stellen
            </Button>
          </div>
        </div>
      </section>
    </>
  )
}

export default CourtReservation
