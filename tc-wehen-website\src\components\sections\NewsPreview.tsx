import React from 'react'
import { Calendar, ArrowRight } from 'lucide-react'
import Card from '../ui/Card'
import Button from '../ui/Button'

const NewsPreview: React.FC = () => {
  // Mock news data - in a real app this would come from a CMS or API
  const news = [
    {
      id: 1,
      title: 'Saisonstart 2024 - Alle Mannschaften bereit',
      excerpt: 'Mit großer Vorfreude starten wir in die neue Tennissaison. Alle Mannschaften sind optimal vorbereitet und freuen sich auf spannende Matches.',
      date: '2024-04-15',
      category: 'Mannschaften',
    },
    {
      id: 2,
      title: 'Neue Trainingszeiten für Kinder und Jugendliche',
      excerpt: 'Ab sofort bieten wir erweiterte Trainingszeiten für unsere jüngsten Mitglieder an. Professionelle Trainer sorgen für optimale Förderung.',
      date: '2024-04-10',
      category: 'Training',
    },
    {
      id: 3,
      title: 'Clubhaus-Renovierung erfolgreich abgeschlossen',
      excerpt: 'Unser Clubhaus erstrahlt in neuem Glanz. Die Renovierungsarbeiten sind abgeschlossen und laden zum gemütlichen Beisammensein ein.',
      date: '2024-04-05',
      category: 'Vereinsleben',
    },
  ]

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('de-DE', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })
  }

  return (
    <section className="section-padding bg-gray-50">
      <div className="container-max">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Aktuelles vom Verein
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Bleiben Sie auf dem Laufenden über Neuigkeiten, Events und wichtige Informationen aus unserem Tennisverein
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {news.map((article) => (
            <Card key={article.id} hover className="h-full flex flex-col">
              <div className="flex items-center justify-between mb-3">
                <span className="bg-accent-100 text-accent-800 text-xs font-medium px-2.5 py-0.5 rounded">
                  {article.category}
                </span>
                <div className="flex items-center text-gray-500 text-sm">
                  <Calendar className="h-4 w-4 mr-1" />
                  {formatDate(article.date)}
                </div>
              </div>
              
              <h3 className="text-xl font-semibold text-gray-900 mb-3 line-clamp-2">
                {article.title}
              </h3>
              
              <p className="text-gray-600 mb-4 flex-grow line-clamp-3">
                {article.excerpt}
              </p>
              
              <button className="flex items-center text-accent-600 hover:text-accent-700 font-medium transition-colors">
                Weiterlesen
                <ArrowRight className="h-4 w-4 ml-1" />
              </button>
            </Card>
          ))}
        </div>
        
        <div className="text-center">
          <Button to="/aktuelles" variant="outline" size="lg">
            Alle Neuigkeiten anzeigen
          </Button>
        </div>
      </div>
    </section>
  )
}

export default NewsPreview
