import React from 'react'
import { Helmet } from 'react-helmet-async'
import { Phone, Mail, MapPin, Clock } from 'lucide-react'
import Card from '../components/ui/Card'
import ContactForm from '../components/ui/ContactForm'

const Contact: React.FC = () => {
  const contactInfo = [
    {
      icon: MapPin,
      title: 'Adresse',
      details: ['Platter Str.', '65232 Taunusstein'],
    },
    {
      icon: Phone,
      title: 'Telefon',
      details: ['Clubhaus: 06128-6598'],
    },
    {
      icon: Mail,
      title: 'E-Mail',
      details: ['<EMAIL>'],
    },
    {
      icon: Clock,
      title: 'Öffnungszeiten',
      details: ['Mo-Fr: 08:00-22:00', 'Sa: 08:00-22:00', 'So: 08:00-20:00'],
    },
  ]

  return (
    <>
      <Helmet>
        <title>Kontakt - TC Wehen e.V. | Tennis Club Taunusstein</title>
        <meta 
          name="description" 
          content="Kontaktieren Sie den TC Wehen e.V. in Taunusstein. Telefon: 06128-6598, E-Mail: <EMAIL>. Wir freuen uns auf Ihre Nachricht!" 
        />
      </Helmet>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-gray-900 to-gray-800 text-white section-padding">
        <div className="container-max">
          <div className="max-w-3xl">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Kontakt
            </h1>
            <p className="text-xl text-gray-200 leading-relaxed">
              Haben Sie Fragen zu unserem Verein, zur Mitgliedschaft oder möchten Sie 
              einfach mehr über den TC Wehen e.V. erfahren? Wir freuen uns auf Ihre Nachricht!
            </p>
          </div>
        </div>
      </section>

      {/* Contact Information */}
      <section className="section-padding bg-white">
        <div className="container-max">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {contactInfo.map((info, index) => {
              const IconComponent = info.icon
              return (
                <Card key={index} className="text-center">
                  <div className="bg-accent-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <IconComponent className="h-8 w-8 text-accent-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">
                    {info.title}
                  </h3>
                  <div className="space-y-1">
                    {info.details.map((detail, detailIndex) => (
                      <p key={detailIndex} className="text-gray-600">
                        {detail}
                      </p>
                    ))}
                  </div>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Contact Form & Map */}
      <section className="section-padding bg-gray-50">
        <div className="container-max">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                Nachricht senden
              </h2>
              <p className="text-gray-600 mb-8">
                Nutzen Sie unser Kontaktformular für Ihre Anfrage. Wir melden uns 
                schnellstmöglich bei Ihnen zurück.
              </p>
              <ContactForm />
            </div>

            {/* Map Placeholder */}
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                So finden Sie uns
              </h2>
              <div className="bg-gray-200 rounded-xl h-96 flex items-center justify-center mb-6">
                <div className="text-center">
                  <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">
                    Karte wird geladen...
                  </p>
                  <p className="text-sm text-gray-400 mt-2">
                    Platter Str., 65232 Taunusstein
                  </p>
                </div>
              </div>
              
              <Card>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Anfahrt
                </h3>
                <p className="text-gray-600 mb-4">
                  Unser Tennisverein liegt verkehrsgünstig in Taunusstein. 
                  Parkplätze sind direkt vor Ort verfügbar.
                </p>
                <div className="space-y-2 text-sm text-gray-600">
                  <p><strong>Mit dem Auto:</strong> A3 Ausfahrt Wiesbaden-Erbenheim</p>
                  <p><strong>Öffentliche Verkehrsmittel:</strong> Bus Linie 274</p>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </section>
    </>
  )
}

export default Contact
