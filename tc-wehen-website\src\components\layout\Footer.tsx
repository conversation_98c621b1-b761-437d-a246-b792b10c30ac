import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { Phone, Mail, MapPin, Facebook } from 'lucide-react'
import logoTCW from '../../assets/Logo-TCW.PNG'

const Footer: React.FC = () => {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="container-max section-padding">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Club Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <img src={logoTCW} alt="TC Wehen Logo" className="h-10 w-auto" />
              <div>
                <h3 className="text-lg font-bold">TC Wehen e.V.</h3>
                <p className="text-gray-400 text-sm">Tennis Club Taunusstein</p>
              </div>
            </div>
            <p className="text-gray-300 text-sm">
              <PERSON><PERSON><PERSON>aftsspieler, <PERSON><PERSON>- und Freizeitspieler, <PERSON><PERSON>, Jugendliche und Erwachsene. 
              Der TC Wehen bietet seinen Mitgliedern Tennis in (fast) jeder Spielstärke.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Schnellzugriff</h4>
            <ul className="space-y-2">
              <li>
                <Link to="/platzreservierung" className="text-gray-300 hover:text-accent-400 transition-colors">
                  Platzreservierung
                </Link>
              </li>
              <li>
                <Link to="/mitgliedschaft" className="text-gray-300 hover:text-accent-400 transition-colors">
                  Mitglied werden
                </Link>
              </li>
              <li>
                <Link to="/training-kurse" className="text-gray-300 hover:text-accent-400 transition-colors">
                  Training buchen
                </Link>
              </li>
              <li>
                <Link to="/aktuelles" className="text-gray-300 hover:text-accent-400 transition-colors">
                  Aktuelles
                </Link>
              </li>
              <li>
                <Link to="/mannschaften" className="text-gray-300 hover:text-accent-400 transition-colors">
                  Mannschaften
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Kontakt</h4>
            <ul className="space-y-3">
              <li className="flex items-center space-x-3">
                <MapPin className="h-5 w-5 text-accent-400 flex-shrink-0" />
                <span className="text-gray-300 text-sm">
                  Platter Str.<br />
                  65232 Taunusstein
                </span>
              </li>
              <li className="flex items-center space-x-3">
                <Phone className="h-5 w-5 text-accent-400 flex-shrink-0" />
                <span className="text-gray-300 text-sm">06128-6598</span>
              </li>
              <li className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-accent-400 flex-shrink-0" />
                <a 
                  href="mailto:<EMAIL>" 
                  className="text-gray-300 hover:text-accent-400 transition-colors text-sm"
                >
                  <EMAIL>
                </a>
              </li>
            </ul>
          </div>

          {/* Social & Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Folgen Sie uns</h4>
            <div className="flex space-x-4 mb-6">
              <a 
                href="#" 
                className="bg-gray-800 p-3 rounded-full hover:bg-accent-600 transition-colors"
                aria-label="Facebook"
              >
                <Facebook className="h-5 w-5" />
              </a>
            </div>
            
            <div className="space-y-2">
              <h5 className="font-medium text-sm">Externe Links</h5>
              <ul className="space-y-1">
                <li>
                  <a 
                    href="https://wehen.tennisplatz.info" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-gray-300 hover:text-accent-400 transition-colors text-sm"
                  >
                    Platzreservierung
                  </a>
                </li>
                <li>
                  <a 
                    href="#" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-gray-300 hover:text-accent-400 transition-colors text-sm"
                  >
                    nuLiga
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © 2024 TC Wehen e.V. Alle Rechte vorbehalten.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <Link to="/datenschutz" className="text-gray-400 hover:text-accent-400 transition-colors text-sm">
                Datenschutz
              </Link>
              <Link to="/impressum" className="text-gray-400 hover:text-accent-400 transition-colors text-sm">
                Impressum
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
