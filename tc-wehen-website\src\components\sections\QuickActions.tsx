import React from 'react'
import { Calendar, Users, GraduationCap, Phone } from 'lucide-react'
import Card from '../ui/Card'
import Button from '../ui/Button'

const QuickActions: React.FC = () => {
  const actions = [
    {
      icon: Calendar,
      title: 'Platz reservieren',
      description: 'Buchen Sie Ihren Tennisplatz online über unser Reservierungssystem',
      buttonText: 'Jetzt reservieren',
      buttonLink: '/platzreservierung',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      icon: Users,
      title: 'Mitglied werden',
      description: 'Werden Sie Teil unserer Tennisfamilie und genießen Sie alle Vorteile',
      buttonText: 'Mitgliedschaft',
      buttonLink: '/mitgliedschaft',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      icon: GraduationCap,
      title: 'Training buchen',
      description: 'Verbessern Sie Ihr Spiel mit professionellem Training für alle Levels',
      buttonText: 'Training finden',
      buttonLink: '/training-kurse',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      icon: Phone,
      title: 'Kontakt aufnehmen',
      description: 'Haben Sie Fragen? Kontaktieren Sie uns gerne telefonisch oder per E-Mail',
      buttonText: 'Kontakt',
      buttonLink: '/kontakt',
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
  ]

  return (
    <section className="section-padding bg-gray-50">
      <div className="container-max">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Schnell zum Ziel
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Nutzen Sie unsere praktischen Services für einen reibungslosen Tennisgenuss
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {actions.map((action, index) => {
            const IconComponent = action.icon
            return (
              <Card key={index} hover className="text-center h-full flex flex-col">
                <div className={`w-16 h-16 ${action.bgColor} rounded-full flex items-center justify-center mx-auto mb-4`}>
                  <IconComponent className={`h-8 w-8 ${action.color}`} />
                </div>
                
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {action.title}
                </h3>
                
                <p className="text-gray-600 mb-6 flex-grow">
                  {action.description}
                </p>
                
                <Button to={action.buttonLink} className="w-full">
                  {action.buttonText}
                </Button>
              </Card>
            )
          })}
        </div>
      </div>
    </section>
  )
}

export default QuickActions
