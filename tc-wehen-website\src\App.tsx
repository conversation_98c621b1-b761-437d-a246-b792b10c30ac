import React from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { HelmetProvider } from 'react-helmet-async'
import Layout from './components/layout/Layout'
import Home from './pages/Home'
import About from './pages/About'
import Courts from './pages/Courts'
import Teams from './pages/Teams'
import Training from './pages/Training'
import Membership from './pages/Membership'
import News from './pages/News'
import Contact from './pages/Contact'
import CourtReservation from './pages/CourtReservation'

function App() {
  return (
    <HelmetProvider>
      <Router>
        <Layout>
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/ueber-uns" element={<About />} />
            <Route path="/plaetze-anlage" element={<Courts />} />
            <Route path="/mannschaften" element={<Teams />} />
            <Route path="/training-kurse" element={<Training />} />
            <Route path="/mitgliedschaft" element={<Membership />} />
            <Route path="/aktuelles" element={<News />} />
            <Route path="/kontakt" element={<Contact />} />
            <Route path="/platzreservierung" element={<CourtReservation />} />
          </Routes>
        </Layout>
      </Router>
    </HelmetProvider>
  )
}

export default App
