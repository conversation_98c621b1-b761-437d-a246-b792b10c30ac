import React from 'react'
import { Helmet } from 'react-helmet-async'
import { GraduationCap, Users, Clock, Star } from 'lucide-react'
import Card from '../components/ui/Card'
import Button from '../components/ui/Button'

const Training: React.FC = () => {
  const programs = [
    {
      icon: Users,
      title: 'Kindertraining',
      age: '6-12 Jahre',
      description: 'Spielerisches Erlernen der Grundlagen mit viel Spaß und Bewegung',
      schedule: 'Mi & Fr 16:00-17:00',
      price: '40€/Monat',
    },
    {
      icon: GraduationCap,
      title: 'Jugendtraining',
      age: '13-18 Jahre',
      description: 'Technikverbesserung und taktische Schulung für Fortgeschrittene',
      schedule: 'Di & Do 17:00-18:30',
      price: '50€/Monat',
    },
    {
      icon: Star,
      title: 'Erwachsenentraining',
      age: 'Ab 18 Jahre',
      description: 'Individuelles Training für alle Spielstärken',
      schedule: 'Nach Vereinbarung',
      price: '60€/Stunde',
    },
    {
      icon: Clock,
      title: '<PERSON><PERSON><PERSON>perku<PERSON>',
      age: 'Alle Altersgruppen',
      description: 'Kostenloser Schnupperkurs für Interessierte',
      schedule: 'Samstags 10:00-11:00',
      price: 'Kostenlos',
    },
  ]

  return (
    <>
      <Helmet>
        <title>Training & Kurse - TC Wehen e.V. | Tennisunterricht Taunusstein</title>
        <meta 
          name="description" 
          content="Professionelles Tennistraining für alle Altersgruppen beim TC Wehen e.V. Kinder, Jugendliche und Erwachsene - jetzt Training buchen!" 
        />
      </Helmet>

      <section className="bg-gradient-to-br from-gray-900 to-gray-800 text-white section-padding">
        <div className="container-max">
          <div className="max-w-3xl">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Training & Kurse
            </h1>
            <p className="text-xl text-gray-200 leading-relaxed">
              Verbessern Sie Ihr Tennisspiel mit professionellem Training. 
              Für alle Altersgruppen und Spielstärken bieten wir passende Kurse an.
            </p>
          </div>
        </div>
      </section>

      <section className="section-padding bg-white">
        <div className="container-max">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {programs.map((program, index) => {
              const IconComponent = program.icon
              return (
                <Card key={index} hover>
                  <div className="flex items-start space-x-4">
                    <div className="bg-accent-100 p-3 rounded-lg flex-shrink-0">
                      <IconComponent className="h-6 w-6 text-accent-600" />
                    </div>
                    <div className="flex-grow">
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        {program.title}
                      </h3>
                      <p className="text-accent-600 font-medium mb-3">
                        {program.age}
                      </p>
                      <p className="text-gray-600 mb-4">
                        {program.description}
                      </p>
                      <div className="space-y-2 text-sm">
                        <p><strong>Zeiten:</strong> {program.schedule}</p>
                        <p><strong>Preis:</strong> {program.price}</p>
                      </div>
                    </div>
                  </div>
                </Card>
              )
            })}
          </div>
          
          <div className="text-center mt-12">
            <Button to="/kontakt" size="lg">
              Training anfragen
            </Button>
          </div>
        </div>
      </section>
    </>
  )
}

export default Training
